{"name": "chat-app-open-ai", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": "18.18.2"}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server.js", "start": "npm run build && npm run server"}, "dependencies": {"react": "^19.1.1", "react-dom": "^19.1.1", "openai": "^4.68.0", "express": "^4.21.2", "cors": "^2.8.5", "dotenv": "^16.4.7"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^5.4.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17"}}